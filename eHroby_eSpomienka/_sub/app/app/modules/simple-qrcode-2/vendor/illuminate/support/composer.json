{"name": "illuminate/support", "description": "The Illuminate Support package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "ext-json": "*", "ext-mbstring": "*", "doctrine/inflector": "^1.4|^2.0", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "nesbot/carbon": "^2.53.1", "voku/portable-ascii": "^1.6.1"}, "conflict": {"tightenco/collect": "<5.5.33"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^8.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^1.3|^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.2.2).", "symfony/process": "Required to use the composer class (^5.4).", "symfony/var-dumper": "Required to use the dd function (^5.4).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}