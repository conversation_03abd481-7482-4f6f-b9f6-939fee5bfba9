<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['utu<PERSON>', 'kyiukonyi'],
    'weekdays' => ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>tu<PERSON>', 'Jumanne', '<PERSON><PERSON>nu', '<PERSON><PERSON><PERSON>', 'I<PERSON><PERSON><PERSON>', 'Jumamosi'],
    'weekdays_short' => ['Jpi', 'Jtt', 'Jnn', 'Jtn', 'Alh', 'Iju', 'Jmo'],
    'weekdays_min' => ['Jpi', 'Jtt', 'Jnn', 'Jtn', 'Alh', 'Iju', 'Jmo'],
    'months' => ['Januari', 'Februari', '<PERSON>hi', 'Aprilyi', '<PERSON>', '<PERSON><PERSON>', 'July<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'months_short' => ['<PERSON>', 'Feb', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Jul', 'A<PERSON>', 'Sep', 'Okt', 'Nov', 'Des'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'DD/MM/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd, D MMMM YYYY HH:mm',
    ],
]);
