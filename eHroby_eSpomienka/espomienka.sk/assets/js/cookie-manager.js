/**
 * <PERSON>ie Manager for GDPR Compliance
 * eSpomienka.sk Memorial Website
 */

class CookieManager {
    constructor() {
        this.cookieName = 'espomienka_cookie_consent';
        this.cookieExpiry = 365; // days
        this.init();
    }

    init() {
        // Check if consent already given
        if (!this.hasConsent()) {
            this.showCookieBanner();
        }
        
        // Initialize cookie settings button
        this.initCookieSettings();
    }

    // Check if user has given consent
    hasConsent() {
        return this.getCookie(this.cookieName) !== null;
    }

    // Get cookie value
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }
        return null;
    }

    // Set cookie
    setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
    }

    // Delete cookie
    deleteCookie(name) {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    }

    // Show cookie banner
    showCookieBanner() {
        const banner = this.createCookieBanner();
        document.body.appendChild(banner);
        
        // Animate in
        setTimeout(() => {
            banner.classList.add('show');
        }, 100);
    }

    // Create cookie banner HTML
    createCookieBanner() {
        const banner = document.createElement('div');
        banner.id = 'cookie-banner';
        banner.className = 'cookie-banner';
        
        banner.innerHTML = `
            <div class="cookie-banner-content">
                <div class="cookie-banner-text">
                    <h4>🍪 Používanie cookies</h4>
                    <p>Táto webstránka používa cookies pre zabezpečenie najlepšej používateľskej skúsenosti. Používame len nevyhnutné cookies pre fungovanie stránky a anonymné štatistiky.</p>
                </div>
                <div class="cookie-banner-actions">
                    <button id="cookie-accept" class="btn btn-primary">Súhlasím</button>
                    <button id="cookie-settings" class="btn btn-secondary">Nastavenia</button>
                    <button id="cookie-reject" class="btn btn-text">Odmietnuť</button>
                </div>
            </div>
        `;

        // Add styles
        this.addCookieBannerStyles();

        // Add event listeners
        banner.querySelector('#cookie-accept').addEventListener('click', () => {
            this.acceptCookies();
            this.hideCookieBanner(banner);
        });

        banner.querySelector('#cookie-reject').addEventListener('click', () => {
            this.rejectCookies();
            this.hideCookieBanner(banner);
        });

        banner.querySelector('#cookie-settings').addEventListener('click', () => {
            this.showCookieSettings();
        });

        return banner;
    }

    // Add cookie banner styles
    addCookieBannerStyles() {
        if (document.getElementById('cookie-banner-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'cookie-banner-styles';
        styles.textContent = `
            .cookie-banner {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(26, 32, 44, 0.95);
                backdrop-filter: blur(10px);
                color: white;
                padding: 1.5rem;
                z-index: 10000;
                transform: translateY(100%);
                transition: transform 0.3s ease;
                border-top: 3px solid #D4A574;
            }
            
            .cookie-banner.show {
                transform: translateY(0);
            }
            
            .cookie-banner-content {
                max-width: 1200px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 2rem;
            }
            
            .cookie-banner-text h4 {
                margin: 0 0 0.5rem 0;
                color: #D4A574;
                font-size: 1.1rem;
            }
            
            .cookie-banner-text p {
                margin: 0;
                font-size: 0.9rem;
                line-height: 1.4;
                opacity: 0.9;
            }
            
            .cookie-banner-actions {
                display: flex;
                gap: 1rem;
                flex-shrink: 0;
            }
            
            .cookie-banner .btn {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
                white-space: nowrap;
            }
            
            .cookie-banner .btn-text {
                background: none;
                color: rgba(255, 255, 255, 0.7);
                border: none;
                text-decoration: underline;
                box-shadow: none;
            }
            
            .cookie-banner .btn-text:hover {
                color: white;
                background: none;
                transform: none;
            }
            
            @media (max-width: 768px) {
                .cookie-banner-content {
                    flex-direction: column;
                    text-align: center;
                }
                
                .cookie-banner-actions {
                    flex-direction: column;
                    width: 100%;
                }
                
                .cookie-banner .btn {
                    width: 100%;
                }
            }
            
            .cookie-settings-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .cookie-settings-modal.show {
                opacity: 1;
                visibility: visible;
            }
            
            .cookie-settings-content {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                max-width: 600px;
                width: 100%;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .cookie-settings-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .cookie-settings-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #718096;
            }
            
            .cookie-category {
                margin-bottom: 1.5rem;
                padding: 1rem;
                border: 1px solid #e2e8f0;
                border-radius: 0.5rem;
            }
            
            .cookie-category-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
            }
            
            .cookie-toggle {
                position: relative;
                width: 50px;
                height: 24px;
                background: #cbd5e0;
                border-radius: 12px;
                cursor: pointer;
                transition: background 0.3s ease;
            }
            
            .cookie-toggle.active {
                background: #D4A574;
            }
            
            .cookie-toggle::after {
                content: '';
                position: absolute;
                top: 2px;
                left: 2px;
                width: 20px;
                height: 20px;
                background: white;
                border-radius: 50%;
                transition: transform 0.3s ease;
            }
            
            .cookie-toggle.active::after {
                transform: translateX(26px);
            }
            
            .cookie-settings-actions {
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
                margin-top: 2rem;
                padding-top: 1rem;
                border-top: 1px solid #e2e8f0;
            }
        `;
        
        document.head.appendChild(styles);
    }

    // Hide cookie banner
    hideCookieBanner(banner) {
        banner.classList.remove('show');
        setTimeout(() => {
            if (banner.parentNode) {
                banner.parentNode.removeChild(banner);
            }
        }, 300);
    }

    // Accept cookies
    acceptCookies() {
        const consent = {
            necessary: true,
            analytics: true,
            timestamp: new Date().toISOString()
        };
        
        this.setCookie(this.cookieName, JSON.stringify(consent), this.cookieExpiry);
        this.enableAnalytics();
    }

    // Reject cookies
    rejectCookies() {
        const consent = {
            necessary: true,
            analytics: false,
            timestamp: new Date().toISOString()
        };
        
        this.setCookie(this.cookieName, JSON.stringify(consent), this.cookieExpiry);
        this.disableAnalytics();
    }

    // Show cookie settings modal
    showCookieSettings() {
        const modal = this.createCookieSettingsModal();
        document.body.appendChild(modal);
        
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    }

    // Create cookie settings modal
    createCookieSettingsModal() {
        const modal = document.createElement('div');
        modal.className = 'cookie-settings-modal';
        
        const currentConsent = this.getCurrentConsent();
        
        modal.innerHTML = `
            <div class="cookie-settings-content">
                <div class="cookie-settings-header">
                    <h3>Nastavenia cookies</h3>
                    <button class="cookie-settings-close">&times;</button>
                </div>
                
                <div class="cookie-category">
                    <div class="cookie-category-header">
                        <h4>Nevyhnutné cookies</h4>
                        <div class="cookie-toggle active" data-disabled="true"></div>
                    </div>
                    <p>Tieto cookies sú nevyhnutné pre správne fungovanie webstránky a nemožno ich vypnúť.</p>
                </div>
                
                <div class="cookie-category">
                    <div class="cookie-category-header">
                        <h4>Analytické cookies</h4>
                        <div class="cookie-toggle ${currentConsent.analytics ? 'active' : ''}" data-category="analytics"></div>
                    </div>
                    <p>Pomáhajú nám pochopiť, ako návštevníci používajú našu webstránku. Všetky údaje sú anonymné.</p>
                </div>
                
                <div class="cookie-settings-actions">
                    <button class="btn btn-secondary" id="cookie-settings-cancel">Zrušiť</button>
                    <button class="btn btn-primary" id="cookie-settings-save">Uložiť nastavenia</button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.cookie-settings-close').addEventListener('click', () => {
            this.hideCookieSettingsModal(modal);
        });

        modal.querySelector('#cookie-settings-cancel').addEventListener('click', () => {
            this.hideCookieSettingsModal(modal);
        });

        modal.querySelector('#cookie-settings-save').addEventListener('click', () => {
            this.saveCookieSettings(modal);
            this.hideCookieSettingsModal(modal);
        });

        // Toggle functionality
        const toggles = modal.querySelectorAll('.cookie-toggle:not([data-disabled])');
        toggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideCookieSettingsModal(modal);
            }
        });

        return modal;
    }

    // Hide cookie settings modal
    hideCookieSettingsModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }

    // Save cookie settings
    saveCookieSettings(modal) {
        const analyticsToggle = modal.querySelector('[data-category="analytics"]');
        
        const consent = {
            necessary: true,
            analytics: analyticsToggle.classList.contains('active'),
            timestamp: new Date().toISOString()
        };
        
        this.setCookie(this.cookieName, JSON.stringify(consent), this.cookieExpiry);
        
        if (consent.analytics) {
            this.enableAnalytics();
        } else {
            this.disableAnalytics();
        }
    }

    // Get current consent
    getCurrentConsent() {
        const consent = this.getCookie(this.cookieName);
        if (consent) {
            try {
                return JSON.parse(consent);
            } catch (e) {
                return { necessary: true, analytics: false };
            }
        }
        return { necessary: true, analytics: false };
    }

    // Initialize cookie settings button
    initCookieSettings() {
        const settingsButton = document.getElementById('cookie-settings');
        if (settingsButton) {
            settingsButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.showCookieSettings();
            });
        }
    }

    // Enable analytics
    enableAnalytics() {
        // Add Google Analytics or other analytics code here
        console.log('Analytics enabled');
    }

    // Disable analytics
    disableAnalytics() {
        // Remove analytics cookies and disable tracking
        console.log('Analytics disabled');
    }
}

// Initialize cookie manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new CookieManager();
});
