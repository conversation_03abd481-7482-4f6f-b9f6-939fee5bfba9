<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Lora & Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Lora:ital,wght@0,400;0,500;0,600;1,400;1,500&display=swap" rel="stylesheet">

    <style>
        /* Základné štýly a premenné */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #fdfdfc; /* --bg-main */
        }

        h1, h2, h3, h4, .font-lora {
            font-family: 'Lora', serif;
        }
        
        :root {
            --brand-green: #22543d;
            --text-light: #6b7280;
        }

        /* Efekt pri prejdení myšou na obrázok v galérii */
        .gallery-item img {
            transition: transform 0.4s ease, filter 0.4s ease;
        }
        .gallery-item:hover img {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* Fade-in animácia pri skrolovaní */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="p-4 md:p-8">

    <!-- Galéria Section -->
    <section id="galeria" class="py-24 bg-[var(--bg-main)]">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-20 fade-in">
                <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-[var(--brand-green)] mb-4">Okamihy zachytené v čase</h2>
                <p class="text-xl text-[var(--text-light)] font-lora italic">Spomienky, ktoré zostávajú</p>
            </div>
            <!-- Mriežka pre galériu -->
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                <!-- Hlavný obrázok, ktorý zaberá viac miesta -->
                <div class="gallery-item col-span-2 row-span-2 fade-in">
                    <img src="assets/images/rodina/vlado_13856_Authentic_candid_family_photo_of_a_Slovak_man_sit_71822ca8-d702-49df-ba37-697a0a83f12b_2.png" alt="Martin s rodinou v horách" class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <!-- Menšie obrázky -->
                <div class="gallery-item fade-in">
                    <img src="assets/images/časozber/mladosť.png" alt="Martin v mladosti s gitarou" class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="assets/images/časozber/detstvo.png" alt="Martin ako dieťa" class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_hiking_thr_05c9a36e-ea22-4248-98ed-5772604a270a_1.png" alt="Martin na túre" class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="assets/images/časozber/profilova.png" alt="Portrét Martina" class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
            </div>
        </div>
    </section>

<script>
    // Skript pre fade-in animáciu, aby bol efekt viditeľný
    document.addEventListener('DOMContentLoaded', () => {
        const fadeInOnScroll = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Pridáme oneskorenie pre krajší efekt postupného načítania
                    const delay = (entry.target.getAttribute('data-delay') || 0) + 'ms';
                    entry.target.style.transitionDelay = delay;
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.fade-in').forEach((el, index) => {
            // Priradíme atribút pre oneskorenie animácie
            el.setAttribute('data-delay', index * 100);
            fadeInOnScroll.observe(el);
        });
    });
</script>

</body>
</html>
