<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Digitálna spomienka na Martina Vargu - ho<PERSON><PERSON><PERSON>cu, fotografa prírody a hudobníka z Liptova">
    <title>Spomienka na Martina Vargu | 1975 - 2023</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Mapbox GL JS -->
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />

    <!-- YouTube Player API -->
    <script src="https://www.youtube.com/iframe_api"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'lora': ['Lora', 'serif'],
                    },
                    colors: {
                        'forest': {
                            50: '#f0f9f0',
                            100: '#dcf2dc',
                            200: '#bce5bc',
                            300: '#8dd18d',
                            400: '#5bb85b',
                            500: '#3a9f3a',
                            600: '#2d7f2d',
                            700: '#256525',
                            800: '#1f5120',
                            900: '#1a431b',
                        },
                        'earth': {
                            50: '#faf8f3',
                            100: '#f4f0e6',
                            200: '#e8dcc7',
                            300: '#d9c5a0',
                            400: '#c8a876',
                            500: '#b8954a',
                            600: '#a6843f',
                            700: '#8a6d35',
                            800: '#715930',
                            900: '#5c4a2a',
                        },
                        'stone': {
                            50: '#fafafa',
                            100: '#f4f4f5',
                            200: '#e4e4e7',
                            300: '#d4d4d8',
                            400: '#a1a1aa',
                            500: '#71717a',
                            600: '#52525b',
                            700: '#3f3f46',
                            800: '#27272a',
                            900: '#18181b',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        .hero-bg {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
            overflow: hidden;
        }

        /* Paralax efekt pre hero pozadie */
        .hero-parallax {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 120%;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            will-change: transform;
            z-index: -1;
        }

        /* Scroll down indikátor */
        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            cursor: pointer;
            animation: bounce 2s infinite;
            z-index: 10;
        }

        .scroll-indicator span {
            font-size: 0.9rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .scroll-arrow {
            width: 24px;
            height: 24px;
            border: 2px solid white;
            border-top: none;
            border-left: none;
            transform: rotate(45deg);
            opacity: 0.8;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        /* Čiasticové animácie - padajúce listy */
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            font-size: 20px;
            color: rgba(255, 255, 255, 0.6);
            animation: fall linear infinite;
            pointer-events: none;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Progress bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #b8954a, #d4a574);
            z-index: 1000;
            transition: width 0.1s ease;
        }

        /* Mobile hamburger menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: #333;
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile menu */
        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            z-index: 999;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .mobile-menu.active {
            transform: translateY(0);
        }

        .mobile-menu a {
            font-size: 2rem;
            margin: 20px 0;
            color: #333;
            text-decoration: none;
            font-family: 'Lora', serif;
            transition: color 0.3s ease;
        }

        .mobile-menu a:hover,
        .mobile-menu a.active {
            color: #b8954a;
        }

        /* Active navigation state */
        nav a.active {
            color: #b8954a !important;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            nav .hidden.md\\:flex {
                display: none !important;
            }

            .mobile-menu {
                display: flex;
            }
        }
        
        /* CSS premenné pre galériu */
        :root {
            --brand-green: #22543d;
            --text-light: #6b7280;
            --bg-main: #fdfdfc;
        }

        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .gallery-item {
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        /* Audio prehrávač CSS */
        .audio-player {
            background: linear-gradient(135deg, #f0f9f0, #dcf2dc);
            border: 2px solid #bce5bc;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .audio-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .audio-btn {
            background: #b8954a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .audio-btn:hover {
            background: #8a6d35;
            transform: scale(1.05);
        }

        .audio-btn.play-pause {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }

        .progress-container {
            flex: 1;
            margin: 0 15px;
            position: relative;
        }

        .progress-bar-audio {
            width: 100%;
            height: 6px;
            background: rgba(184, 149, 74, 0.3);
            border-radius: 3px;
            cursor: pointer;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #b8954a, #d4a574);
            border-radius: 3px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            font-size: 14px;
            color: #666;
            min-width: 80px;
        }

        .track-info {
            text-align: center;
            margin-bottom: 15px;
        }

        .track-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .track-artist {
            font-size: 14px;
            color: #666;
        }

        .playlist {
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .playlist-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-bottom: 5px;
        }

        .playlist-item:hover {
            background: rgba(184, 149, 74, 0.1);
        }

        .playlist-item.active {
            background: rgba(184, 149, 74, 0.2);
            border-left: 4px solid #b8954a;
        }

        .playlist-item .track-number {
            width: 30px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }

        .playlist-item .track-details {
            flex: 1;
            text-align: left;
            margin-left: 10px;
        }

        .playlist-item .track-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .playlist-item .track-duration {
            font-size: 12px;
            color: #666;
        }

        .download-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .download-btn:hover {
            background: #45a049;
        }

        /* Volume control */
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            justify-content: center;
        }

        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(184, 149, 74, 0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }

        .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #b8954a;
            border-radius: 50%;
            cursor: pointer;
        }

        .volume-icon {
            font-size: 18px;
            color: #666;
        }

        /* Mapa a citáty CSS */
        .map-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }

        /* Mapbox mapa styling */
        #map {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Mapbox popup styling */
        .mapboxgl-popup-content {
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .mapboxgl-popup-close-button {
            color: #b8954a;
            font-size: 20px;
        }

        .mapboxgl-popup-close-button:hover {
            color: #8a6d35;
        }

        /* YouTube Player styling */
        #youtube-player-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        #youtube-player iframe {
            border-radius: 10px;
        }

        .map-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #b8954a;
            position: relative;
            overflow: hidden;
        }

        .map-placeholder::before {
            content: '🏔️';
            font-size: 4rem;
            opacity: 0.3;
            position: absolute;
            top: 20px;
            left: 20px;
        }

        .map-placeholder::after {
            content: '🌲';
            font-size: 3rem;
            opacity: 0.3;
            position: absolute;
            bottom: 20px;
            right: 20px;
        }

        .map-info {
            text-align: center;
            color: #666;
        }

        .map-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .map-point {
            background: linear-gradient(135deg, rgba(212, 165, 116, 0.1), rgba(232, 196, 160, 0.1));
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #b8954a;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .map-point:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(212, 165, 116, 0.2);
        }

        .map-point-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #b8954a;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .map-point-description {
            color: #666;
            line-height: 1.6;
        }

        /* Citáty carousel */
        .quotes-carousel {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .quote-slide {
            display: none;
            text-align: center;
            animation: fadeInQuote 0.5s ease-in-out;
        }

        .quote-slide.active {
            display: block;
        }

        .quote-text {
            font-size: 1.5rem;
            font-style: italic;
            color: #333;
            line-height: 1.8;
            margin-bottom: 20px;
            font-family: 'Lora', serif;
        }

        .quote-author {
            font-size: 1rem;
            color: #b8954a;
            font-weight: 600;
        }

        .quote-context {
            font-size: 0.9rem;
            color: #666;
            margin-top: 10px;
        }

        .carousel-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 30px;
        }

        .carousel-btn {
            background: #b8954a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .carousel-btn:hover {
            background: #8a6d35;
            transform: scale(1.1);
        }

        .carousel-indicators {
            display: flex;
            gap: 10px;
        }

        .carousel-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(184, 149, 74, 0.3);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .carousel-dot.active {
            background: #b8954a;
        }

        @keyframes fadeInQuote {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Jemné predely medzi sekciami */
        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(184, 149, 74, 0.3), transparent);
            margin: 3rem 0;
        }

        /* Timeline štýly podľa vzoru */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .header-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: url('./assets/images/časozber/profilova.png') center/cover;
            border: 5px solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header-photo:hover::before {
            opacity: 1;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            font-style: italic;
        }

        .timeline {
            position: relative;
            max-width: 900px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 40px 0;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .timeline-item.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .timeline-item:nth-child(odd) .timeline-content {
            left: 0;
            text-align: right;
            padding-right: 40px;
        }

        .timeline-item:nth-child(even) .timeline-content {
            left: 50%;
            text-align: left;
            padding-left: 40px;
        }

        .timeline-content {
            position: relative;
            width: 50%;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .content-photo {
            width: 100%;
            height: 180px;
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .content-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .timeline-content:hover .content-photo::before {
            opacity: 1;
        }

        .photo-overlay {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .timeline-content:hover .photo-overlay {
            opacity: 1;
            transform: translateY(0);
        }

        /* Timeline modal pre detailný pohľad */
        .timeline-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .timeline-dot {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        .timeline-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .year {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .phase {
            font-size: 1.3rem;
            color: #764ba2;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .description {
            font-size: 1rem;
            line-height: 1.6;
            color: #555;
        }

        /* Ikony odstránené */

        .timeline-modal.active {
            display: flex;
        }

        .timeline-modal-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .timeline-modal.active .timeline-modal-content {
            transform: scale(1);
        }

        .timeline-modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 30px;
            cursor: pointer;
            color: #999;
            transition: color 0.3s ease;
        }

        .timeline-modal-close:hover {
            color: #333;
        }

        .memorial {
            text-align: center;
            margin-top: 60px;
            padding: 30px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            position: relative;
        }

        .memorial-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 30px 0;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .memorial-photo {
            aspect-ratio: 1;
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .memorial-photo:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .memorial-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .memorial-photo:hover::before {
            opacity: 1;
        }

        .memorial h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 2rem;
        }

        .memorial p {
            font-style: italic;
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .heart {
            color: #e74c3c;
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .timeline::before {
                left: 30px;
            }

            .timeline-item .timeline-content {
                width: calc(100% - 80px);
                left: 60px !important;
                text-align: left !important;
                padding-left: 20px !important;
                padding-right: 20px !important;
            }

            .timeline-dot {
                left: 30px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }

        /* Heartbeat animácia - ponechané */
        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .heart {
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        /* Staré štýly odstránené - používame nový dizajn */

        /* Staré mobile štýly odstránené - používame nový dizajn */

            /* Hero section mobile */
            .hero-bg h1 {
                font-size: 3rem !important;
            }

            .hero-bg p {
                font-size: 1.2rem !important;
            }

            .scroll-indicator {
                bottom: 20px;
            }

            /* Progress bar pre mobile */
            .progress-bar {
                height: 2px;
            }

            /* Staré štýly odstránené */
        }

        /* Galéria CSS */
        .gallery-item {
            cursor: pointer;
            transition: transform 0.4s ease, filter 0.4s ease;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
        }

        .gallery-item img {
            transition: transform 0.4s ease, filter 0.4s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* Lightbox pre galériu */
        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .lightbox.active {
            display: flex;
        }

        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 10px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .lightbox-close {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 40px;
            color: white;
            cursor: pointer;
            z-index: 2001;
            transition: opacity 0.3s ease;
        }

        .lightbox-close:hover {
            opacity: 0.7;
        }

        .lightbox-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 30px;
            color: white;
            cursor: pointer;
            padding: 20px;
            user-select: none;
            transition: opacity 0.3s ease;
            z-index: 2001;
        }

        .lightbox-nav:hover {
            opacity: 0.7;
        }

        .lightbox-prev {
            left: 20px;
        }

        .lightbox-next {
            right: 20px;
        }

        /* Mobile swipe indikátor */
        .swipe-indicator {
            display: none;
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .swipe-indicator {
                display: block;
            }

            .lightbox-nav {
                display: none;
            }

            .gallery-item {
                margin-bottom: 10px;
            }
        }
        /* Accessibility features */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .focus-visible:focus {
            outline: 2px solid #b8954a;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .timeline-content {
                border: 2px solid #000;
            }

            .audio-btn {
                border: 2px solid #000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .particle {
                display: none;
            }
        }

        /* Skip to content link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #b8954a;
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        }

        .skip-link:focus {
            top: 6px;
        }
    </style>
</head>
<body class="font-inter bg-stone-50">
    <!-- Skip to content link -->
    <a href="#main-content" class="skip-link">Preskočiť na hlavný obsah</a>

    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar" role="progressbar" aria-label="Pozícia na stránke"></div>

    <!-- Sticky Navigation -->
    <nav class="fixed top-0 w-full bg-white/90 backdrop-blur-sm shadow-sm z-50" role="navigation" aria-label="Hlavná navigácia">
        <div class="max-w-6xl mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-lora font-semibold text-stone-800">Spomienka na Martina Vargu</h1>
                <div class="hidden md:flex space-x-8" role="menubar">
                    <a href="#zivot" class="nav-link text-stone-600 hover:text-forest-600 transition-colors focus-visible" role="menuitem">Život</a>
                    <a href="#timeline" class="nav-link text-stone-600 hover:text-forest-600 transition-colors focus-visible" role="menuitem">Životná cesta</a>
                    <a href="#galeria" class="nav-link text-stone-600 hover:text-forest-600 transition-colors focus-visible" role="menuitem">Galéria</a>
                    <a href="#hudba" class="nav-link text-stone-600 hover:text-forest-600 transition-colors focus-visible" role="menuitem">Hudba</a>
                    <a href="#spomienky" class="nav-link text-stone-600 hover:text-forest-600 transition-colors focus-visible" role="menuitem">Spomienky</a>
                </div>
                <button class="hamburger focus-visible" id="hamburger" aria-label="Otvoriť mobilné menu" aria-expanded="false">
                    <span class="sr-only">Menu</span>
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <a href="#zivot" class="mobile-nav-link">Život</a>
        <a href="#timeline" class="mobile-nav-link">Životná cesta</a>
        <a href="#galeria" class="mobile-nav-link">Galéria</a>
        <a href="#hudba" class="mobile-nav-link">Hudba</a>
        <a href="#spomienky" class="mobile-nav-link">Spomienky</a>
    </div>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section -->
        <section class="hero-bg min-h-screen flex items-center justify-center text-white" aria-label="Úvodná sekcia">
        <div class="hero-parallax"></div>
        <div class="particles-container" id="particles"></div>

        <div class="text-center px-6 max-w-4xl mx-auto relative z-10">
            <h1 class="text-6xl md:text-8xl font-lora font-light mb-4 fade-in">Martin Varga</h1>
            <p class="text-2xl md:text-3xl font-light mb-8 fade-in">1975 – 2023</p>
            <div class="max-w-3xl mx-auto fade-in">
                <p class="text-lg md:text-xl leading-relaxed">
                    Človek, ktorý našiel svoj pokoj v tichu hôr a ktorého odkaz žije v každom štíte,
                    ktorý zdolal, v každej piesni, ktorú zložil, a v srdciach tých, ktorých sa dotkol.
                </p>
            </div>
        </div>

        <div class="scroll-indicator" onclick="scrollToNextSection()">
            <span>Pokračovať</span>
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Jemný predel -->
    <div class="section-divider"></div>

    <!-- Život Section -->
    <section id="zivot" class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Jeho Cesta</h2>
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="fade-in">
                    <img src="./assets/images/časozber/profilova.png"
                         alt="Martin Varga - horský vodca"
                         class="rounded-lg shadow-lg w-full h-96 object-cover">
                </div>
                <div class="fade-in">
                    <p class="text-lg leading-relaxed text-stone-700 mb-6">
                        Martin sa narodil v Liptovskom Mikuláši a celý svoj život zasvätil horám. 
                        Ako horský vodca sprevádzal stovky ľudí po majestátnych chodníkoch Vysokých Tatier, 
                        kde sa cítil najviac doma.
                    </p>
                    <p class="text-lg leading-relaxed text-stone-700 mb-6">
                        Jeho fotoaparát bol jeho verným spoločníkom, ktorým zachytával prchavé momenty 
                        krásy prírody. Po večeroch pri krbe často bral do rúk gitaru a skladal piesne 
                        o horách, slobode a priateľstve.
                    </p>
                    <p class="text-lg leading-relaxed text-stone-700">
                        Jeho život bol tichou oslavou všetkého, čo je skutočné a trvácne.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Jemný predel -->
    <div class="section-divider"></div>

    <!-- Timeline Section - Opravený podľa vzoru -->
    <section id="timeline" class="py-20 bg-stone-100">
        <div class="container">
            <!-- Hlavička -->
            <div class="header">
                <div class="header-photo"></div>
                <h1>Martin Varga</h1>
                <p>1975 – 2024 • Životná cesta plná lásky, hudby a hôr</p>
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/časozber/detstvo.png'); background-position: center -50px;">
                            <div class="photo-overlay">Tatry - miesto, kde sa všetko začalo</div>
                        </div>
                        <div class="year">1975</div>
                        <div class="phase">Narodenie</div>
                        <div class="description">
                            Martin sa narodil v júni 1975 v podhorí Vysokých Tatier, v malej dedinke, kde boli hory každodennou súčasťou života. Už ako malý chlapec si najradšej hral vonku – v lese zbieral šišky a počúval spev vtákov.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/časozber/mladosť.png'); background-position: center -40px;">
                            <div class="photo-overlay">Prvé akordy pri večernom západe slnka</div>
                        </div>
                        <div class="year">1990-1993</div>
                        <div class="phase">Mladosť a hudba</div>
                        <div class="description">
                            Ako teenager objavil svoju vášeň pre hudbu. Dostal starú akustickú gitaru po strýkovi a trávil hodiny nacvičovaním. Často si sadol na okraj lesa, hral a spieval, zatiaľ čo slnko zapadalo za hory.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/časozber/mladý_2.png');">
                            <div class="photo-overlay">Vysokohorské chodníky boli jeho druhým domovom</div>
                        </div>
                        <div class="year">1995-2000</div>
                        <div class="phase">Prvé kroky do dospelosti</div>
                        <div class="description">
                            Po ukončení školy pracoval ako sprievodca v horskom stredisku. Turistom z celého sveta s nadšením rozprával o Tatrách a ukazoval im skryté chodníky, ktoré poznal od detstva.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_with_his_f_5b771139-0bfa-4b90-8676-27cd6bc7d3ac_1.png');">
                            <div class="photo-overlay">Prvé stretnutie pri horskom jazere</div>
                        </div>
                        <div class="year">2000</div>
                        <div class="phase">Láska a začiatok rodiny</div>
                        <div class="description">
                            Spoznal Máriu, s ktorou ich spojila láska k prírode a podobné hodnoty. Zosobášili sa a o dva roky neskôr sa im narodila prvá dcéra. O tri roky k nej pribudol syn.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/časozber/rodina.png'); background-position: center -60px;">
                            <div class="photo-overlay">Rodinné túry boli najkrajšími chvíľami</div>
                        </div>
                        <div class="year">2002-2015</div>
                        <div class="phase">Rodinné štastie</div>
                        <div class="description">
                            Rodina sa stala Martinovým svetom. Voľné dni trávili na horských chatách, v zime na bežkách, v lete na turistike. Učil deti rozoznávať stopy zvierat a chrániť prírodu.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/rodina/vlado_13856_Candid_photorealistic_photo_of_a_Slovak_man_in_hi_affc4612-143f-4665-999b-36cc4afd7034_2.png');">
                            <div class="photo-overlay">Pokojné chvíle pri písaní spomienok</div>
                        </div>
                        <div class="year">2015-2020</div>
                        <div class="phase">Zrelosť a výzvy</div>
                        <div class="description">
                            V štyridsiatke diagnostikovali Martinovi srdcovú vadu. Naučil sa vychutnávať pomalšie prechádzky a začal písať krátke poviedky o svojich zážitkoch a spomienkach z hôr.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/rodina/vlado_13856_Candid_photograph_of_a_Slovak_man_in_his_30s_with_5271ac2f-d23d-4971-8263-354de99b7818_2.png');">
                            <div class="photo-overlay">Posledné spoločné prechádzky k potoku</div>
                        </div>
                        <div class="year">2020-2024</div>
                        <div class="phase">Posledné roky</div>
                        <div class="description">
                            Aj keď už nemohol vystúpiť na najvyššie vrcholy, Martinove oči sa vždy rozžiarili, keď sa pozeral na končiare, ktoré kedysi zdolal. Každú jar vyšli s manželkou na symbolickú prechádzku k lesnému potoku.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="content-photo" style="background-image: url('./assets/images/časozber/spomienka.png'); background-position: center bottom;">
                            <div class="photo-overlay">Večný pokoj v objatí hôr</div>
                        </div>
                        <div class="year">Marec 2024</div>
                        <div class="phase">Posledná rozlúčka</div>
                        <div class="description">
                            Vo veku 48 rokov odišiel náhle, ale pokojne. Jeho posledné chvíle patrili rodine. Zanechal za sebou nielen milujúcu manželku a deti, ale aj spomienky v srdciach tých, ktorých inšpiroval.
                        </div>
                    </div>
                </div>
            </div>

            <div class="memorial">
                <h2>Spomíname <span class="heart">♥</span></h2>
                <div class="memorial-gallery">
                    <div class="memorial-photo" style="background-image: url('./assets/images/časozber/detstvo.png');"></div>
                    <div class="memorial-photo" style="background-image: url('./assets/images/časozber/mladosť.png');"></div>
                    <div class="memorial-photo" style="background-image: url('./assets/images/časozber/rodina.png');"></div>
                    <div class="memorial-photo" style="background-image: url('./assets/images/časozber/spomienka.png');"></div>
                </div>
                <p>
                    Martin nás naučil, že skutočné bohatstvo sa neskrýva v majetku, ale v láske k blízkym,
                    pokore pred prírodou a radosti z maličkostí. Hory, ktoré miloval, budú navždy niesť jeho tichý odkaz.
                </p>
            </div>
        </div>
    </section>

    <!-- Timeline Modal -->
    <div class="timeline-modal" id="timelineModal">
        <div class="timeline-modal-content">
            <span class="timeline-modal-close" id="timelineModalClose">&times;</span>
            <div id="timelineModalBody">
                <!-- Obsah sa dynamicky načíta -->
            </div>
        </div>
    </div>

    <!-- Lightbox pre galériu -->
    <div class="lightbox" id="lightbox">
        <span class="lightbox-close" id="lightboxClose">&times;</span>
        <div class="lightbox-nav lightbox-prev" id="lightboxPrev">&#8249;</div>
        <div class="lightbox-nav lightbox-next" id="lightboxNext">&#8250;</div>
        <div class="lightbox-content">
            <img id="lightboxImg" src="" alt="">
            <div class="swipe-indicator">
                Swipe left/right pre navigáciu
            </div>
        </div>
    </div>

    <!-- Jemný predel -->
    <div class="section-divider"></div>

    <!-- Galéria Section -->
    <section id="galeria" class="py-24 bg-stone-100">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-20 fade-in">
                <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-green-800 mb-4">Okamihy zachytené v čase</h2>
                <p class="text-xl text-gray-600 font-lora italic">Spomienky, ktoré zostávajú</p>
            </div>
            <!-- Mriežka pre galériu -->
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                <!-- Hlavný obrázok, ktorý zaberá viac miesta -->
                <div class="gallery-item col-span-2 row-span-2 fade-in">
                    <img src="./assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_with_his_f_5b771139-0bfa-4b90-8676-27cd6bc7d3ac_1.png"
                         alt="Martin s rodinou v horách"
                         class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <!-- Menšie obrázky -->
                <div class="gallery-item fade-in">
                    <img src="./assets/images/časozber/mladosť.png"
                         alt="Martin v mladosti s gitarou"
                         class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="./assets/images/časozber/detstvo.png"
                         alt="Martin ako dieťa"
                         class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="./assets/images/rodina/vlado_13856_Candid_photorealistic_photo_of_a_Slovak_man_in_hi_affc4612-143f-4665-999b-36cc4afd7034_2.png"
                         alt="Martin na túre"
                         class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
                <div class="gallery-item fade-in">
                    <img src="./assets/images/časozber/mladý_2.png"
                         alt="Portrét Martina"
                         class="rounded-lg shadow-lg w-full h-full object-cover cursor-pointer">
                </div>
            </div>
        </div>
    </section>

    <!-- Jemný predel -->
    <div class="section-divider"></div>

    <!-- Hudba Section -->
    <section id="hudba" class="py-20 bg-stone-50">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <h2 class="text-4xl font-lora font-semibold mb-16 text-stone-800 fade-in">Tóny jeho duše</h2>

            <div class="audio-player fade-in" id="audioPlayer">
                <!-- Track Info -->
                <div class="track-info">
                    <div class="track-title" id="trackTitle">Banska Bystrica</div>
                    <div class="track-artist">Honza Nedved</div>
                </div>

                <!-- YouTube Player Container -->
                <div id="youtube-player-container" style="margin: 20px 0;">
                    <div id="youtube-player"></div>
                </div>

                <!-- Custom Controls -->
                <div class="audio-controls" role="group" aria-label="Ovládanie prehrávania hudby">
                    <button class="audio-btn focus-visible" id="prevBtn" aria-label="Predchádzajúca skladba">⏮</button>
                    <button class="audio-btn play-pause focus-visible" id="playPauseBtn" aria-label="Prehrať/Pozastaviť">▶</button>
                    <button class="audio-btn focus-visible" id="nextBtn" aria-label="Ďalšia skladba">⏭</button>

                    <div class="time-display">
                        <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                    </div>
                </div>

                <!-- Volume Control -->
                <div class="volume-control">
                    <span class="volume-icon">🔊</span>
                    <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="70">
                </div>

                <!-- Playlist -->
                <div class="playlist" id="playlist">
                    <div class="playlist-item active" data-track="0">
                        <div class="track-number">1</div>
                        <div class="track-details">
                            <div class="track-name">Banska Bystrica</div>
                            <div class="track-duration">4:12</div>
                        </div>
                        <a href="https://youtube.com/watch?v=Ywg2pvva-wg" target="_blank" class="download-btn">🎵 YouTube</a>
                    </div>
                    <div class="playlist-item" data-track="1">
                        <div class="track-number">2</div>
                        <div class="track-details">
                            <div class="track-name">Tam u nebeských bran</div>
                            <div class="track-duration">3:45</div>
                        </div>
                        <a href="https://youtube.com/watch?v=lRca7evReSs" target="_blank" class="download-btn">🎵 YouTube</a>
                    </div>
                    <div class="playlist-item" data-track="2">
                        <div class="track-number">3</div>
                        <div class="track-details">
                            <div class="track-name">Rosa na kolejích</div>
                            <div class="track-duration">5:23</div>
                        </div>
                        <a href="https://youtube.com/watch?v=Epkxt9kmTjE" target="_blank" class="download-btn">🎵 YouTube</a>
                    </div>
                </div>


            </div>

            <blockquote class="text-xl italic text-stone-600 font-lora fade-in mt-8">
                „V každom tóne gitary je ozvena ticha, ktoré počuť len tam hore, bližšie k nebu."
            </blockquote>
        </div>
    </section>

    <!-- Mapa a Citáty Section -->
    <section class="py-20 bg-white">
        <div class="max-w-6xl mx-auto px-6">
            <!-- Mapa obľúbených miest -->
            <div class="mb-20">
                <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Obľúbené miesta v Tatrách</h2>

                <div class="map-container fade-in">
                    <div id="map" style="width: 100%; height: 400px; border-radius: 10px;"></div>
                </div>

                    <div class="map-points">
                        <div class="map-point">
                            <div class="map-point-title">
                                🏔️ Štrbské Pleso
                            </div>
                            <div class="map-point-description">
                                Martinovo najobľúbenejšie miesto. Sem chodil na ranné prechádzky a tu vznikla jeho najznámejšia skladba "Ranná Rosa na Štrbskom Plese".
                            </div>
                        </div>

                        <div class="map-point">
                            <div class="map-point-title">
                                ⛰️ Skalnaté Pleso
                            </div>
                            <div class="map-point-description">
                                Miesto, kde Martin našiel pokoj a inšpiráciu. Často tu sedával s gitarou a pozoroval odraz vrcholov vo vode.
                            </div>
                        </div>

                        <div class="map-point">
                            <div class="map-point-title">
                                🌲 Tatranská Lomnica
                            </div>
                            <div class="map-point-description">
                                Východiskový bod mnohých Martinových túr. Odtiaľto sa vydával na svoje obľúbené chodníky s rodinou.
                            </div>
                        </div>

                        <div class="map-point">
                            <div class="map-point-title">
                                🏞️ Popradské Pleso
                            </div>
                            <div class="map-point-description">
                                Kľudné miesto, kde Martin rád trávil čas s deťmi. Učil ich tu rozoznávať rastliny a zvieratá.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Citáty carousel -->
            <div>
                <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Martinove myšlienky</h2>

                <div class="quotes-carousel fade-in" id="quotesCarousel">
                    <div class="quote-slide active">
                        <div class="quote-text">
                            "Život je ako horská túra – nie je dôležité, ako vysoko sa dostaneš, ale koľko krásnych chvíľ prežiješ na ceste."
                        </div>
                        <div class="quote-author">Martin Varga</div>
                        <div class="quote-context">Povedané pri poslednej rodinnej túre, 2022</div>
                    </div>

                    <div class="quote-slide">
                        <div class="quote-text">
                            "V tichu hôr počujem hudbu, ktorú žiadny nástroj nedokáže zahrať. Je to symfónia prírody, ktorá hrá len pre tých, čo vedia počúvať."
                        </div>
                        <div class="quote-author">Martin Varga</div>
                        <div class="quote-context">Z jeho denníka, 2020</div>
                    </div>

                    <div class="quote-slide">
                        <div class="quote-text">
                            "Najkrajšie poklady sa nenachádzajú na vrcholoch, ale v srdciach ľudí, s ktorými ich zdieľaš."
                        </div>
                        <div class="quote-author">Martin Varga</div>
                        <div class="quote-context">Venované svojej rodine, 2021</div>
                    </div>

                    <div class="quote-slide">
                        <div class="quote-text">
                            "Každý východ slnka je nový začiatok. Každý západ je poďakovaním za prežitý deň."
                        </div>
                        <div class="quote-author">Martin Varga</div>
                        <div class="quote-context">Ranné úvahy na Štrbskom Plese, 2019</div>
                    </div>

                    <div class="carousel-controls">
                        <button class="carousel-btn" id="prevQuote">‹</button>
                        <div class="carousel-indicators" id="quoteIndicators">
                            <div class="carousel-dot active" data-slide="0"></div>
                            <div class="carousel-dot" data-slide="1"></div>
                            <div class="carousel-dot" data-slide="2"></div>
                            <div class="carousel-dot" data-slide="3"></div>
                        </div>
                        <button class="carousel-btn" id="nextQuote">›</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Jemný predel -->
    <div class="section-divider"></div>

    <!-- Kondolencia kniha Section -->
    <section id="spomienky" class="py-20 bg-stone-50">
        <div class="max-w-6xl mx-auto px-6">
            <h2 class="text-4xl font-lora font-semibold text-center mb-16 text-stone-800 fade-in">Kniha spomienok</h2>

            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Formulár pre pridanie spomienky -->
                <div class="bg-white rounded-lg p-8 shadow-lg fade-in">
                    <h3 class="text-2xl font-lora font-medium mb-6 text-stone-700">Zanechajte svoju spomienku</h3>

                    <form id="memoryForm" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-stone-700 mb-2">Vaše meno</label>
                            <input type="text" id="authorName" required
                                   class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-forest-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-stone-700 mb-2">Vaša spomienka</label>
                            <textarea id="memoryText" rows="5" required
                                      class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-forest-500 focus:border-transparent"
                                      placeholder="Podeľte sa o svoju spomienku na Martina..."></textarea>
                        </div>



                        <button type="submit"
                                class="w-full bg-forest-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-forest-700 transition-colors">
                            Pridať spomienku
                        </button>
                    </form>

                    <!-- Virtuálna sviečka -->
                    <div class="mt-8 text-center">
                        <h4 class="text-lg font-medium text-stone-700 mb-4">Zapáliť virtuálnu sviečku</h4>
                        <button id="lightCandle"
                                class="inline-flex items-center gap-2 bg-forest-600 text-white px-6 py-3 rounded-lg hover:bg-forest-700 transition-colors">
                            <span class="candle-icon">🕯️</span>
                            Zapáliť sviečku
                        </button>
                        <div id="candleCount" class="mt-2 text-sm text-stone-600">
                            Zapálených sviečok: <span id="candleNumber">127</span>
                        </div>
                    </div>
                </div>

                <!-- Zobrazenie spomienok -->
                <div class="bg-white rounded-lg p-8 shadow-lg fade-in">
                    <h3 class="text-2xl font-lora font-medium mb-6 text-stone-700">Posledné spomienky</h3>

                    <div id="memoriesContainer" class="space-y-6 max-h-96 overflow-y-auto">
                        <!-- Ukážkové spomienky -->
                        <div class="memory-item border-b border-stone-200 pb-4">
                            <div class="flex items-start gap-3">
                                <div class="w-10 h-10 bg-stone-100 rounded-full flex items-center justify-center">
                                    <span class="text-forest-600 font-medium">M</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="font-medium text-stone-800">Mária K.</span>
                                        <span class="text-sm text-stone-500">pred 2 hodinami</span>
                                    </div>
                                    <p class="text-stone-700 leading-relaxed">
                                        Martin bol výnimočný človek. Vždy mal čas na rozhovor a jeho úsmev dokázal rozjasniť aj najtemnejší deň. Budem na neho navždy spomínať.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="memory-item border-b border-stone-200 pb-4">
                            <div class="flex items-start gap-3">
                                <div class="w-10 h-10 bg-stone-100 rounded-full flex items-center justify-center">
                                    <span class="text-forest-600 font-medium">P</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="font-medium text-stone-800">Peter S.</span>
                                        <span class="text-sm text-stone-500">pred 5 hodinami</span>
                                    </div>
                                    <p class="text-stone-700 leading-relaxed">
                                        Spolu sme zdolali mnoho vrcholov. Martin ma naučil, že v horách nejde len o výkon, ale o to, aby sme si užili každý krok cesty.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="memory-item border-b border-stone-200 pb-4">
                            <div class="flex items-start gap-3">
                                <div class="w-10 h-10 bg-stone-100 rounded-full flex items-center justify-center">
                                    <span class="text-forest-600 font-medium">A</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="font-medium text-stone-800">Anna V.</span>
                                        <span class="text-sm text-stone-500">včera</span>
                                    </div>
                                    <p class="text-stone-700 leading-relaxed">
                                        Jeho hudba ma vždy dojala. Keď hral na gitare pri táboráku, čas sa zastavil. Ďakujem za všetky krásne chvíle.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer class="bg-stone-800 text-stone-300 py-8">
        <div class="max-w-6xl mx-auto px-6 text-center">
            <p>&copy; 2024 Rodina Vargová | S láskou vytvorené na pamiatku nášho Martina.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Pridáme oneskorenie pre krajší efekt postupného načítania
                    const delay = (entry.target.getAttribute('data-delay') || 0) + 'ms';
                    entry.target.style.transitionDelay = delay;
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe all fade-in elements and pridáme data-delay atribút
        document.querySelectorAll('.fade-in').forEach((el, index) => {
            // Priradíme atribút pre oneskorenie animácie
            el.setAttribute('data-delay', index * 100);
            observer.observe(el);
        });

        // Timeline animation observer - opravený pre nový dizajn
        const timelineItems = document.querySelectorAll('.timeline-item');

        const timelineObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        timelineItems.forEach(item => {
            timelineObserver.observe(item);
        });

        // Timeline content click interactions - opravený pre nový dizajn
        const timelineContents = document.querySelectorAll('.timeline-content');

        timelineContents.forEach(content => {
            content.addEventListener('click', function() {
                // Dočasne zvýrazní kliknutý element
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';

                setTimeout(() => {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                }, 300);
            });

            // Hover effects pre nový dizajn
            content.addEventListener('mouseenter', function() {
                this.style.borderLeft = '4px solid #f59e0b';
            });

            content.addEventListener('mouseleave', function() {
                this.style.borderLeft = '';
            });
        });

        // Odstránené - hviezdy už nie sú potrebné pre zjednodušený dizajn

        // Zatvorenie timeline modalu
        function closeTimelineModal() {
            timelineModal.classList.remove('active');
        }

        if (timelineModalClose) {
            timelineModalClose.addEventListener('click', closeTimelineModal);
        }

        if (timelineModal) {
            timelineModal.addEventListener('click', function(e) {
                if (e.target === timelineModal) {
                    closeTimelineModal();
                }
            });
        }

        // ESC klávesa pre zatvorenie modalu
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && timelineModal && timelineModal.classList.contains('active')) {
                closeTimelineModal();
            }
        });

        // Initial fade-in for hero elements
        setTimeout(() => {
            document.querySelectorAll('.hero-bg .fade-in').forEach(el => {
                el.classList.add('visible');
            });
        }, 500);

        // Paralax efekt pre hero sekciu
        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallaxElement = document.querySelector('.hero-parallax');
            if (parallaxElement) {
                const speed = scrolled * 0.5;
                parallaxElement.style.transform = `translateY(${speed}px)`;
            }
        }

        // Scroll down indikátor funkcia
        function scrollToNextSection() {
            const nextSection = document.querySelector('#zivot');
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Vytvorenie padajúcich častíc
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particles = ['🍂', '🍃', '❄️', '✨'];

            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.textContent = particles[Math.floor(Math.random() * particles.length)];

                // Náhodná pozícia a rýchlosť
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
                particle.style.animationDelay = Math.random() * 2 + 's';

                particlesContainer.appendChild(particle);

                // Odstránenie častice po animácii
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 5000);
            }

            // Vytvorenie častíc v intervaloch
            setInterval(createParticle, 800);
        }

        // Event listenery
        window.addEventListener('scroll', updateParallax);

        // Spustenie častíc po načítaní stránky
        window.addEventListener('load', () => {
            createParticles();
        });

        // Progress bar funkcionality
        function updateProgressBar() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.getElementById('progressBar').style.width = scrollPercent + '%';
        }

        // Mobile hamburger menu
        function toggleMobileMenu() {
            const hamburger = document.getElementById('hamburger');
            const mobileMenu = document.getElementById('mobileMenu');

            hamburger.classList.toggle('active');
            mobileMenu.classList.toggle('active');
        }

        // Active navigation state
        function updateActiveNavigation() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

            let currentSection = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;

                if (window.pageYOffset >= sectionTop &&
                    window.pageYOffset < sectionTop + sectionHeight) {
                    currentSection = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + currentSection) {
                    link.classList.add('active');
                }
            });
        }

        // Smooth scroll pre navigačné odkazy
        function setupSmoothScroll() {
            const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // Zatvorenie mobile menu
                        const mobileMenu = document.getElementById('mobileMenu');
                        const hamburger = document.getElementById('hamburger');
                        if (mobileMenu.classList.contains('active')) {
                            mobileMenu.classList.remove('active');
                            hamburger.classList.remove('active');
                        }
                    }
                });
            });
        }

        // Event listenery pre navigáciu
        document.getElementById('hamburger').addEventListener('click', toggleMobileMenu);
        window.addEventListener('scroll', () => {
            updateProgressBar();
            updateActiveNavigation();
        });

        // Inicializácia - odstránené, presunuté nižšie

        // Galéria lightbox a swipe gestá
        function setupGalleryLightbox() {
            const galleryItems = document.querySelectorAll('.gallery-item img');
            const lightbox = document.getElementById('lightbox');
            const lightboxImg = document.getElementById('lightboxImg');
            const lightboxClose = document.getElementById('lightboxClose');
            const lightboxPrev = document.getElementById('lightboxPrev');
            const lightboxNext = document.getElementById('lightboxNext');

            let currentImageIndex = 0;
            let images = [];

            // Vytvorenie zoznamu obrázkov
            galleryItems.forEach((img, index) => {
                images.push({
                    src: img.src,
                    alt: img.alt
                });

                img.addEventListener('click', () => {
                    currentImageIndex = index;
                    showLightbox();
                });
            });

            function showLightbox() {
                if (images.length > 0) {
                    lightboxImg.src = images[currentImageIndex].src;
                    lightboxImg.alt = images[currentImageIndex].alt;
                    lightbox.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }

            function hideLightbox() {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }

            function showNextImage() {
                currentImageIndex = (currentImageIndex + 1) % images.length;
                lightboxImg.src = images[currentImageIndex].src;
                lightboxImg.alt = images[currentImageIndex].alt;
            }

            function showPrevImage() {
                currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
                lightboxImg.src = images[currentImageIndex].src;
                lightboxImg.alt = images[currentImageIndex].alt;
            }

            // Event listenery
            lightboxClose.addEventListener('click', hideLightbox);
            lightboxNext.addEventListener('click', showNextImage);
            lightboxPrev.addEventListener('click', showPrevImage);

            lightbox.addEventListener('click', (e) => {
                if (e.target === lightbox) {
                    hideLightbox();
                }
            });

            // Keyboard navigácia
            document.addEventListener('keydown', (e) => {
                if (lightbox.classList.contains('active')) {
                    switch(e.key) {
                        case 'Escape':
                            hideLightbox();
                            break;
                        case 'ArrowLeft':
                            showPrevImage();
                            break;
                        case 'ArrowRight':
                            showNextImage();
                            break;
                    }
                }
            });

            // Touch/swipe gestá pre mobile
            let touchStartX = 0;
            let touchEndX = 0;

            lightbox.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
            });

            lightbox.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = touchStartX - touchEndX;

                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0) {
                        // Swipe left - next image
                        showNextImage();
                    } else {
                        // Swipe right - previous image
                        showPrevImage();
                    }
                }
            }
        }

        // YouTube Player funkcionality
        let youtubePlayer;
        let currentTrack = 0;
        let isPlaying = false;

        // YouTube playlist data
        const youtubePlaylist = [
            {
                title: 'Banska Bystrica',
                artist: 'Honza Nedved',
                videoId: 'Ywg2pvva-wg',
                duration: '4:12'
            },
            {
                title: 'Tam u nebeských bran',
                artist: 'Michal Tučný',
                videoId: 'lRca7evReSs',
                duration: '3:45'
            },
            {
                title: 'Rosa na kolejích',
                artist: 'Wabi Daněk',
                videoId: 'Epkxt9kmTjE',
                duration: '5:23'
            }
        ];

        // YouTube API ready callback
        function onYouTubeIframeAPIReady() {
            setupYouTubePlayer();
        }

        function setupYouTubePlayer() {
            const playPauseBtn = document.getElementById('playPauseBtn');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const currentTimeSpan = document.getElementById('currentTime');
            const totalTimeSpan = document.getElementById('totalTime');
            const volumeSlider = document.getElementById('volumeSlider');
            const trackTitle = document.getElementById('trackTitle');
            const playlistItems = document.querySelectorAll('.playlist-item');

            // Vytvorenie YouTube prehrávača
            youtubePlayer = new YT.Player('youtube-player', {
                height: '200',
                width: '100%',
                videoId: youtubePlaylist[0].videoId,
                playerVars: {
                    'playsinline': 1,
                    'controls': 0, // Skryjeme YouTube kontroly
                    'rel': 0,
                    'showinfo': 0,
                    'modestbranding': 1
                },
                events: {
                    'onReady': onPlayerReady,
                    'onStateChange': onPlayerStateChange
                }
            });

            function onPlayerReady(event) {
                loadTrack(currentTrack);
                updateTimeDisplay();
            }

            function onPlayerStateChange(event) {
                if (event.data == YT.PlayerState.PLAYING) {
                    isPlaying = true;
                    playPauseBtn.textContent = '⏸';
                    updateTimeDisplay();
                } else if (event.data == YT.PlayerState.PAUSED) {
                    isPlaying = false;
                    playPauseBtn.textContent = '▶';
                } else if (event.data == YT.PlayerState.ENDED) {
                    nextTrack();
                }
            }

            function loadTrack(index) {
                const track = youtubePlaylist[index];
                if (youtubePlayer && youtubePlayer.loadVideoById) {
                    youtubePlayer.loadVideoById(track.videoId);
                    trackTitle.textContent = track.title;

                    // Update playlist active state
                    playlistItems.forEach((item, i) => {
                        item.classList.toggle('active', i === index);
                    });

                    // Reset time display
                    currentTimeSpan.textContent = '0:00';
                    totalTimeSpan.textContent = track.duration;
                }
            }

            function togglePlayPause() {
                if (youtubePlayer) {
                    if (isPlaying) {
                        youtubePlayer.pauseVideo();
                    } else {
                        youtubePlayer.playVideo();
                    }
                }
            }

            function previousTrack() {
                currentTrack = (currentTrack - 1 + youtubePlaylist.length) % youtubePlaylist.length;
                loadTrack(currentTrack);
            }

            function nextTrack() {
                currentTrack = (currentTrack + 1) % youtubePlaylist.length;
                loadTrack(currentTrack);
            }

            function formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            }

            function updateTimeDisplay() {
                if (youtubePlayer && youtubePlayer.getCurrentTime) {
                    const currentTime = youtubePlayer.getCurrentTime();
                    currentTimeSpan.textContent = formatTime(currentTime);

                    if (isPlaying) {
                        setTimeout(updateTimeDisplay, 1000);
                    }
                }
            }

            // Event listeners
            playPauseBtn.addEventListener('click', togglePlayPause);
            prevBtn.addEventListener('click', previousTrack);
            nextBtn.addEventListener('click', nextTrack);

            // Volume control
            volumeSlider.addEventListener('input', (e) => {
                if (youtubePlayer && youtubePlayer.setVolume) {
                    youtubePlayer.setVolume(e.target.value);
                }
            });

            // Playlist item clicks
            playlistItems.forEach((item, index) => {
                item.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('download-btn')) {
                        currentTrack = index;
                        loadTrack(currentTrack);
                    }
                });
            });

            // Set initial volume
            if (youtubePlayer && youtubePlayer.setVolume) {
                youtubePlayer.setVolume(70);
            }
        }

        // Fallback ak YouTube API zlyhá
        function setupAudioPlayer() {
            if (typeof YT === 'undefined' || !YT.Player) {
                console.warn('YouTube API sa nepodarilo načítať, používam fallback');
                // Môžeme pridať fallback riešenie alebo len skryť prehrávač
                const audioPlayer = document.getElementById('audioPlayer');
                if (audioPlayer) {
                    audioPlayer.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <h3 style="color: #b8954a; margin-bottom: 15px;">🎵 Hudba Martina Vargu</h3>
                            <p>Prehrávač hudby momentálne nie je dostupný.</p>
                            <p style="margin-top: 10px;">
                                <a href="https://youtube.com/playlist?list=PLexample" target="_blank"
                                   style="color: #b8954a; text-decoration: underline;">
                                   Počúvajte na YouTube
                                </a>
                            </p>
                        </div>
                    `;
                }
            } else {
                setupYouTubePlayer();
            }
        }

        // YouTube link function - už nie je potrebná, linky sú priamo v HTML

        // Citáty carousel funkcionality
        function setupQuotesCarousel() {
            const slides = document.querySelectorAll('.quote-slide');
            const indicators = document.querySelectorAll('.carousel-dot');
            const prevBtn = document.getElementById('prevQuote');
            const nextBtn = document.getElementById('nextQuote');

            let currentSlide = 0;
            let autoSlideInterval;

            function showSlide(index) {
                // Hide all slides
                slides.forEach(slide => slide.classList.remove('active'));
                indicators.forEach(dot => dot.classList.remove('active'));

                // Show current slide
                slides[index].classList.add('active');
                indicators[index].classList.add('active');

                currentSlide = index;
            }

            function nextSlide() {
                const next = (currentSlide + 1) % slides.length;
                showSlide(next);
            }

            function prevSlide() {
                const prev = (currentSlide - 1 + slides.length) % slides.length;
                showSlide(prev);
            }

            function startAutoSlide() {
                autoSlideInterval = setInterval(nextSlide, 5000); // 5 sekúnd
            }

            function stopAutoSlide() {
                clearInterval(autoSlideInterval);
            }

            // Event listenery
            nextBtn.addEventListener('click', () => {
                nextSlide();
                stopAutoSlide();
                startAutoSlide(); // Restart auto-slide
            });

            prevBtn.addEventListener('click', () => {
                prevSlide();
                stopAutoSlide();
                startAutoSlide(); // Restart auto-slide
            });

            // Indicator clicks
            indicators.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    showSlide(index);
                    stopAutoSlide();
                    startAutoSlide(); // Restart auto-slide
                });
            });

            // Pause auto-slide on hover
            const carousel = document.getElementById('quotesCarousel');
            carousel.addEventListener('mouseenter', stopAutoSlide);
            carousel.addEventListener('mouseleave', startAutoSlide);

            // Start auto-slide
            startAutoSlide();
        }

        // Kondolencia kniha funkcionality s Airtable
        function setupMemoryBook() {
            const memoryForm = document.getElementById('memoryForm');
            const memoriesContainer = document.getElementById('memoriesContainer');
            const lightCandleBtn = document.getElementById('lightCandle');
            const candleNumber = document.getElementById('candleNumber');

            // Airtable konfigurácia
            const AIRTABLE_API_KEY = '**********************************************************************************';
            const AIRTABLE_BASE_ID = 'appjrq70ohFM9hrvM';
            const AIRTABLE_TABLE_ID = 'tblKbwEEyfqUXhfyh';

            let candleCount = parseInt(localStorage.getItem('candleCount') || '127');
            candleNumber.textContent = candleCount;

            // Najprv otestujeme štruktúru tabuľky
            testAirtableStructure();

            // Načítanie existujúcich spomienok pri načítaní stránky
            loadMemoriesFromAirtable();

            // Pridanie novej spomienky
            memoryForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const authorName = document.getElementById('authorName').value;
                const memoryText = document.getElementById('memoryText').value;

                if (authorName && memoryText) {
                    try {
                        // Uloženie do Airtable
                        await saveMemoryToAirtable(authorName, memoryText);

                        // Zobrazenie na stránke
                        addMemoryToPage(authorName, memoryText, 'práve teraz');

                        memoryForm.reset();
                        alert('Ďakujeme za vašu spomienku! Bola pridaná do knihy spomienok.');
                    } catch (error) {
                        console.error('Chyba pri ukladaní spomienky:', error);
                        alert(`Nastala chyba pri ukladaní spomienky: ${error.message}. Skúste to znovu.`);
                    }
                }
            });

            // Test štruktúry Airtable tabuľky
            async function testAirtableStructure() {
                try {
                    console.log('=== TESTOVANIE AIRTABLE ŠTRUKTÚRY ===');
                    const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}?maxRecords=1`, {
                        headers: {
                            'Authorization': `Bearer ${AIRTABLE_API_KEY}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log('Úspešne načítané dáta z Airtable');

                        if (data.records && data.records.length > 0) {
                            const firstRecord = data.records[0];
                            console.log('Dostupné polia v tabuľke:', Object.keys(firstRecord.fields));
                            console.log('Ukážkový záznam:', firstRecord.fields);

                            // Uložíme názvy polí pre použitie
                            window.AIRTABLE_FIELD_NAMES = Object.keys(firstRecord.fields);
                        } else {
                            console.log('Tabuľka je prázdna, nemôžem zistiť názvy polí');
                        }
                    } else {
                        const errorData = await response.text();
                        console.error('Chyba pri testovaní Airtable:', response.status, errorData);
                    }
                } catch (error) {
                    console.error('Chyba pri testovaní Airtable štruktúry:', error);
                }
                console.log('=== KONIEC TESTOVANIA ===');
            }

            // Uloženie spomienky do Airtable
            async function saveMemoryToAirtable(meno, spomienka) {
                console.log('Pokúšam sa uložiť spomienku:', { meno, spomienka });

                console.log('Používam tabuľku ID:', AIRTABLE_TABLE_ID);

                const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AIRTABLE_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fields: {
                            "meno": meno,
                            "spomienka": spomienka,
                            "datum": new Date().toISOString().split('T')[0], // Len dátum bez času
                            "schvalene": true // Automaticky schválené
                        }
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error('Airtable error response:', errorData);
                    console.error('Request URL:', `https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}`);
                    console.error('Request body:', JSON.stringify({
                        fields: {
                            "meno": meno,
                            "spomienka": spomienka,
                            "datum": new Date().toISOString().split('T')[0],
                            "schvalene": true
                        }
                    }));

                    // Pokúsime sa zistiť konkrétny problém
                    if (response.status === 403) {
                        throw new Error(`Nemáte oprávnenia na zápis do tabuľky "${AIRTABLE_TABLE_ID}". Skontrolujte API kľúč a oprávnenia.`);
                    } else if (response.status === 404) {
                        throw new Error(`Tabuľka "${AIRTABLE_TABLE_ID}" nebola nájdená v base "${AIRTABLE_BASE_ID}".`);
                    } else if (response.status === 422) {
                        throw new Error(`Neplatné dáta pre tabuľku "${AIRTABLE_TABLE_ID}". Skontrolujte názvy polí.`);
                    } else {
                        throw new Error(`Airtable API error: ${response.status} - ${errorData}`);
                    }
                }

                const result = await response.json();
                console.log('Úspešne uložené:', result);
                return result;
            }

            // Načítanie spomienok z Airtable
            async function loadMemoriesFromAirtable() {
                try {
                    console.log('Načítavam spomienky z Airtable...');
                    console.log('Base ID:', AIRTABLE_BASE_ID);
                    console.log('Table ID:', AIRTABLE_TABLE_ID);

                    // Načítame spomienky z tabuľky (bez sortovania zatiaľ)
                    const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}?maxRecords=10`, {
                        headers: {
                            'Authorization': `Bearer ${AIRTABLE_API_KEY}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log('Načítané spomienky:', data);

                        // Vymazanie ukážkových spomienok
                        memoriesContainer.innerHTML = '';

                        // Pridanie skutočných spomienok
                        data.records.forEach(record => {
                            const fields = record.fields;
                            console.log('Načítavam záznam:', fields);

                            // Používame presné názvy polí
                            const meno = fields.meno || 'Anonymný';
                            const spomienka = fields.spomienka || 'Bez textu';
                            const datum = fields.datum;

                            const timeAgo = datum ? formatTimeAgo(datum) : 'nedávno';
                            addMemoryToPage(meno, spomienka, timeAgo);
                        });
                    } else {
                        console.error('Chyba pri načítavaní spomienok:', response.status, await response.text());
                        // Zobrazíme ukážkové spomienky ak API nefunguje
                        showSampleMemories();
                    }
                } catch (error) {
                    console.error('Chyba pri načítavaní spomienok:', error);
                    // Zobrazíme ukážkové spomienky ak API nefunguje
                    showSampleMemories();
                }
            }

            // Formátovanie času
            function formatTimeAgo(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffTime = Math.abs(now - date);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays === 1) return 'včera';
                if (diffDays < 7) return `pred ${diffDays} dňami`;
                if (diffDays < 30) return `pred ${Math.ceil(diffDays / 7)} týždňami`;
                return `pred ${Math.ceil(diffDays / 30)} mesiacmi`;
            }

            // Zapálenie virtuálnej sviečky
            lightCandleBtn.addEventListener('click', function() {
                candleCount++;
                candleNumber.textContent = candleCount;
                localStorage.setItem('candleCount', candleCount.toString());

                // Animácia sviečky
                this.style.transform = 'scale(1.1)';
                this.style.background = '#f97316';

                setTimeout(() => {
                    this.style.transform = '';
                    this.style.background = '';
                }, 200);

                // Zobrazenie poďakovania
                const thankYou = document.createElement('div');
                thankYou.textContent = '✨ Sviečka zapálená';
                thankYou.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    z-index: 1000;
                    font-size: 18px;
                `;

                document.body.appendChild(thankYou);

                setTimeout(() => {
                    document.body.removeChild(thankYou);
                }, 2000);
            });

            // Pridanie spomienky na stránku (vizuálne)
            function addMemoryToPage(author, text, timeAgo) {
                const memoryItem = document.createElement('div');
                memoryItem.className = 'memory-item border-b border-stone-200 pb-4';

                const initial = author.charAt(0).toUpperCase();

                memoryItem.innerHTML = `
                    <div class="flex items-start gap-3">
                        <div class="w-10 h-10 bg-stone-100 rounded-full flex items-center justify-center">
                            <span class="text-forest-600 font-medium">${initial}</span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="font-medium text-stone-800">${author}</span>
                                <span class="text-sm text-stone-500">${timeAgo}</span>
                            </div>
                            <p class="text-stone-700 leading-relaxed">${text}</p>
                        </div>
                    </div>
                `;

                // Pridanie na začiatok zoznamu
                memoriesContainer.insertBefore(memoryItem, memoriesContainer.firstChild);

                // Animácia pridania
                memoryItem.style.opacity = '0';
                memoryItem.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    memoryItem.style.transition = 'all 0.5s ease';
                    memoryItem.style.opacity = '1';
                    memoryItem.style.transform = 'translateY(0)';
                }, 100);
            }

            // Zobrazenie ukážkových spomienok ako fallback
            function showSampleMemories() {
                console.log('Zobrazujem ukážkové spomienky...');
                memoriesContainer.innerHTML = '';

                const sampleMemories = [
                    {
                        meno: 'Anna Nováková',
                        spomienka: 'Martin bol úžasný človek s veľkým srdcom. Vždy pomohol, keď bolo treba. Bude nám veľmi chýbať.',
                        cas: 'pred 2 dňami'
                    },
                    {
                        meno: 'Peter Svoboda',
                        spomienka: 'Spomínam si na naše spoločné chvíle plné smiechu. Martin mal dar rozveseliť každého okolo seba.',
                        cas: 'pred týždňom'
                    },
                    {
                        meno: 'Mária Horváthová',
                        spomienka: 'Bol to človek s veľkým srdcom a láskavou dušou. Nech odpočíva v pokoji.',
                        cas: 'pred 10 dňami'
                    }
                ];

                sampleMemories.forEach(memory => {
                    addMemoryToPage(memory.meno, memory.spomienka, memory.cas);
                });
            }
        }

        // Performance a accessibility funkcie
        function setupPerformanceOptimizations() {
            // Preload kritických zdrojov
            const preloadLinks = [
                { href: './assets/css/styles.css', as: 'style' },
                { href: './assets/js/main.js', as: 'script' }
            ];

            preloadLinks.forEach(link => {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.href = link.href;
                preloadLink.as = link.as;
                document.head.appendChild(preloadLink);
            });
        }

        function setupAccessibilityFeatures() {
            // Keyboard navigácia pre carousel
            document.addEventListener('keydown', (e) => {
                if (e.target.closest('.quotes-carousel')) {
                    if (e.key === 'ArrowLeft') {
                        document.getElementById('prevQuote').click();
                    } else if (e.key === 'ArrowRight') {
                        document.getElementById('nextQuote').click();
                    }
                }
            });

            // Focus management pre mobile menu
            const hamburger = document.getElementById('hamburger');
            const mobileMenu = document.getElementById('mobileMenu');

            hamburger.addEventListener('click', () => {
                const isExpanded = hamburger.getAttribute('aria-expanded') === 'true';
                hamburger.setAttribute('aria-expanded', !isExpanded);

                if (!isExpanded) {
                    // Focus na prvý link v mobile menu
                    setTimeout(() => {
                        const firstLink = mobileMenu.querySelector('a');
                        if (firstLink) firstLink.focus();
                    }, 100);
                }
            });

            // Escape key pre zatvorenie modalov
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Zatvorenie mobile menu
                    if (mobileMenu.classList.contains('active')) {
                        hamburger.click();
                        hamburger.focus();
                    }
                }
            });

            // Announce progress bar changes
            window.addEventListener('scroll', () => {
                const progressBar = document.getElementById('progressBar');
                const progress = progressBar.style.width;
                if (progress) {
                    progressBar.setAttribute('aria-valuenow', parseInt(progress));
                    progressBar.setAttribute('aria-valuemin', '0');
                    progressBar.setAttribute('aria-valuemax', '100');
                }
            });

            // Reduced motion detection
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (prefersReducedMotion.matches) {
                document.body.classList.add('reduced-motion');
            }
        }

        // Error handling a fallbacks
        function setupErrorHandling() {
            // YouTube Player error handling
            if (typeof YT === 'undefined') {
                console.warn('YouTube API sa nepodarilo načítať');
                const trackTitle = document.getElementById('trackTitle');
                if (trackTitle) {
                    trackTitle.textContent = 'Prehrávač momentálne nedostupný';
                }
            }

            // Image error handling
            document.addEventListener('error', (e) => {
                if (e.target.tagName === 'IMG') {
                    e.target.style.display = 'none';
                    console.warn('Obrázok sa nepodarilo načítať:', e.target.src);
                }
            }, true);
        }

        // Mapbox mapa setup
        function setupMapbox() {
            // Mapbox - 50,000 načítaní mesačne ZADARMO
            mapboxgl.accessToken = 'pk.eyJ1IjoidmxhZG8zNjEiLCJhIjoiY21jc3dkemE1MGpsdTJrczlndnIyMnY3eiJ9.UMO7y7Q4211EV3hTAbfCOA';

            try {
                const map = new mapboxgl.Map({
                    container: 'map',
                    style: 'mapbox://styles/mapbox/outdoors-v12', // Perfektné pre hory
                    center: [20.0892, 49.0880], // Vysoké Tatry
                    zoom: 10
                });

                // Pridanie Martinových obľúbených miest
                const martinovaMiesta = [
                    {
                        coordinates: [20.0651, 49.1193],
                        title: "Štrbské pleso",
                        description: "Martinovo najobľúbenejšie miesto na fotenie úsvitov"
                    },
                    {
                        coordinates: [20.1317, 49.1794],
                        title: "Skalnaté pleso",
                        description: "Miesto prvého horoledeckého úspechu"
                    },
                    {
                        coordinates: [20.1667, 49.1667],
                        title: "Tatranská Lomnica",
                        description: "Východiskový bod mnohých Martinových túr"
                    },
                    {
                        coordinates: [20.0833, 49.0833],
                        title: "Popradské Pleso",
                        description: "Kľudné miesto, kde Martin rád trávil čas s deťmi"
                    }
                ];

                martinovaMiesta.forEach(miesto => {
                    new mapboxgl.Marker({ color: '#2d7f2d' })
                        .setLngLat(miesto.coordinates)
                        .setPopup(new mapboxgl.Popup().setHTML(
                            `<h3 style="color: #b8954a; margin-bottom: 8px; font-family: 'Lora', serif;">${miesto.title}</h3><p style="color: #666; line-height: 1.4;">${miesto.description}</p>`
                        ))
                        .addTo(map);
                });

                // Pridanie navigačných kontrol
                map.addControl(new mapboxgl.NavigationControl());

                // Pridanie fullscreen kontroly
                map.addControl(new mapboxgl.FullscreenControl());

            } catch (error) {
                console.warn('Mapbox sa nepodarilo načítať:', error);
                // Fallback - zobrazenie placeholder správy
                const mapContainer = document.getElementById('map');
                if (mapContainer) {
                    mapContainer.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #e8f5e8, #d4edda); border-radius: 10px; border: 2px dashed #b8954a;">
                            <div style="text-align: center; color: #666;">
                                <h3 style="font-size: 1.5rem; margin-bottom: 10px; color: #b8954a;">🏔️ Mapa Tatier</h3>
                                <p>Interaktívna mapa momentálne nie je dostupná</p>
                            </div>
                        </div>
                    `;
                }
            }
        }

        // Inicializácia všetkých komponentov
        document.addEventListener('DOMContentLoaded', () => {
            setupSmoothScroll();
            updateProgressBar();
            updateActiveNavigation();
            setupGalleryLightbox();
            setupAudioPlayer();
            setupQuotesCarousel();
            setupMemoryBook();
            setupPerformanceOptimizations();
            setupAccessibilityFeatures();
            setupErrorHandling();
            setupMapbox();
        });
    </script>
</body>
</html>
